# 统一钻井检测系统

## 概述

统一钻井检测系统是一个集成了三种钻井算法的融合检测平台：

1. **前驱信号检测**（基于PatchTST）- 权重 0.5
2. **异常检测**（基于FEDformer）- 权重 0.3  
3. **专家经验**（规则引擎）- 权重 0.2

系统采用适配器模式设计，支持灵活的配置和扩展。

## 核心特性

- ✅ **统一接口**：所有算法使用统一的CSV输入格式
- ✅ **特征对齐**：自动将不同格式的数据对齐到10维标准特征
- ✅ **权重融合**：支持自定义算法权重配置
- ✅ **批量处理**：支持单文件和批量文件处理
- ✅ **适配器模式**：易于扩展和维护
- ✅ **配置管理**：支持配置文件和参数自定义

## 文件结构

```
mix_algo/
├── unified_detection_system.py    # 主检测系统
├── algorithm_adapters.py          # 算法适配器
├── unified_config.py              # 配置管理
├── example_usage.py               # 使用示例
├── README_统一检测系统.md         # 说明文档
├── 前驱信号检测/                  # 前驱信号检测算法
├── 异常检测/                      # 异常检测算法
└── 专家经验/                      # 专家经验算法
```

## 快速开始

### 1. 环境要求

```bash
Python 3.7+
pandas
numpy
scikit-learn
torch (用于深度学习算法)
```

### 2. 基本使用

#### 单文件检测

```python
from unified_detection_system import UnifiedDetectionSystem

# 创建检测系统
detector = UnifiedDetectionSystem()

# 检测单个文件
result = detector.detect_single_file("your_data.csv")

print(f"风险分数: {result['risk']:.3f}")
print(f"预测标签: {result['label']}")
```

#### 批量检测

```python
# 批量检测目录中的所有CSV文件
output_file = detector.batch_detect(
    input_dir="./data",
    pattern="*.csv",
    output_file="results.csv"
)
```

#### 命令行使用

```bash
# 单文件检测
python unified_detection_system.py data.csv -o result.csv

# 批量检测
python unified_detection_system.py ./data_dir --batch -o batch_results.csv

# 自定义权重
python unified_detection_system.py data.csv --precursor-weight 0.6 --anomaly-weight 0.2 --expert-weight 0.2
```

### 3. 数据格式要求

输入数据必须是CSV格式，包含以下10个标准特征：

| 列名 | 描述 | 单位 |
|------|------|------|
| DEP | 井深 | 米 |
| BITDEP | 钻头位置 | 米 |
| HOKHEI | 大钩高度 | 米 |
| DRITIME | 迟到时间 | 分钟 |
| WOB | 钻压 | 千牛 |
| HKLD | 大钩负荷 | 千牛 |
| RPM | 转盘转速 | 转/分 |
| TOR | 扭矩 | 千牛·米 |
| SPP | 立压 | 兆帕 |
| date | 时间戳 | 时间格式 |

**注意**：系统支持中文列名自动映射，如"井深"→"DEP"。

### 4. 输出格式

检测结果包含以下字段：

```python
{
    'filename': '文件名',
    'risk': 0.75,           # 风险分数 (0-1)
    'label': 1,             # 预测标签 (0=正常, 1=预警)
    'details': {            # 详细结果
        'precursor': {...}, # 前驱信号检测结果
        'anomaly': {...},   # 异常检测结果
        'expert': {...}     # 专家经验结果
    }
}
```

## 高级配置

### 1. 自定义权重

```python
# 自定义算法权重
custom_weights = {
    'precursor': 0.4,
    'anomaly': 0.4, 
    'expert': 0.2
}

detector = UnifiedDetectionSystem(weights=custom_weights)
```

### 2. 配置文件

```python
# 创建示例配置文件
from unified_config import UnifiedConfig
UnifiedConfig.create_sample_config("my_config.json")

# 使用配置文件
config = UnifiedConfig.load_config_from_file("my_config.json")
detector = UnifiedDetectionSystem(config=config)
```

### 3. 算法参数调整

```python
# 修改专家系统阈值
config = UnifiedConfig.get_default_config()
config['expert']['score_threshold'] = 3
config['expert']['window_size'] = 20

# 修改异常检测阈值
config['anomaly']['anomaly_threshold'] = 15.0

detector = UnifiedDetectionSystem(config=config)
```

## 算法详情

### 前驱信号检测
- **模型**：PatchTST
- **输入**：10维特征，动态序列长度
- **输出**：风险分数 + 预测标签
- **权重**：0.5（默认）

### 异常检测
- **模型**：FEDformer
- **输入**：12维特征（自动扩展），固定96序列长度
- **输出**：重建误差 + 异常比例
- **权重**：0.3（默认）

### 专家经验
- **方法**：规则引擎 + 机器学习
- **输入**：7个规则特征，30窗口大小
- **输出**：规则得分 + 置信度
- **权重**：0.2（默认）

## 融合策略

系统采用加权融合策略：

1. **风险分数融合**：`融合风险 = w1×前驱风险 + w2×异常风险 + w3×专家风险`
2. **标签融合**：`融合标签 = 1 if 加权标签分数 ≥ 0.5 else 0`
3. **容错机制**：单个算法失败时使用默认值，不影响整体检测

## 性能优化

- **缓存机制**：训练数据预处理结果缓存
- **并行处理**：批量检测支持并行处理（待实现）
- **内存优化**：大文件分块处理（待实现）

## 故障排除

### 常见问题

1. **ImportError**: 确保所有依赖库已安装
2. **FileNotFoundError**: 检查算法脚本路径是否正确
3. **特征缺失**: 系统会自动填充缺失特征，但建议提供完整数据

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
detector = UnifiedDetectionSystem()
result = detector.detect_single_file("data.csv")
```

## 扩展开发

### 添加新算法

1. 继承`BaseAlgorithmAdapter`类
2. 实现必要的方法
3. 在`AdapterFactory`中注册
4. 更新配置文件

```python
class NewAlgorithmAdapter(BaseAlgorithmAdapter):
    def preprocess_data(self, data):
        # 数据预处理逻辑
        pass
    
    def run_algorithm(self, data_file):
        # 算法执行逻辑
        pass
    
    def parse_output(self, output):
        # 输出解析逻辑
        pass
```

## 版本历史

- **v1.0.0** (2025-01-27): 初始版本，支持三算法融合
- 后续版本将添加更多功能和优化

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请联系开发团队。
