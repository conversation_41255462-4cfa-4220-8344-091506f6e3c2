#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
片段处理示例
演示统一检测系统的3分钟片段处理功能，兼容前驱信号检测的工作方式

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from unified_detection_system import UnifiedDetectionSystem
from data_segmentation import DataSegmentation, SegmentResultProcessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_long_drilling_data(output_file: str = "long_drilling_data.csv", 
                             duration_hours: int = 2) -> str:
    """
    创建长时间钻井数据，包含多个卡钻时间段
    
    Args:
        output_file: 输出文件路径
        duration_hours: 数据持续时间（小时）
        
    Returns:
        输出文件路径
    """
    logger.info(f"创建长时间钻井数据: {output_file}, 持续时间: {duration_hours}小时")
    
    # 每5分钟一个数据点
    num_points = duration_hours * 12
    np.random.seed(42)
    
    # 生成时间序列
    start_time = datetime(2025, 1, 27, 8, 0, 0)
    time_series = [start_time + timedelta(minutes=5*i) for i in range(num_points)]
    
    # 生成基础钻井数据
    data = {
        'date': time_series,
        'DEP': np.cumsum(np.random.normal(0.5, 0.1, num_points)) + 3000,  # 井深递增
        'BITDEP': np.cumsum(np.random.normal(0.5, 0.1, num_points)) + 3000,  # 钻头位置
        'HOKHEI': np.random.normal(25, 2, num_points),  # 大钩高度
        'DRITIME': np.random.normal(20, 5, num_points),  # 迟到时间
        'WOB': np.random.normal(150, 30, num_points),  # 钻压
        'HKLD': np.random.normal(900, 100, num_points),  # 大钩负荷
        'RPM': np.random.normal(80, 15, num_points),  # 转盘转速
        'TOR': np.random.normal(20, 5, num_points),  # 扭矩
        'SPP': np.random.normal(25, 5, num_points),  # 立压
    }
    
    # 模拟多个卡钻时间段（每个持续6-15分钟）
    stuck_periods = [
        (15, 18),   # 第1个卡钻：1小时15分 - 1小时30分
        (35, 40),   # 第2个卡钻：2小时55分 - 3小时20分
        (55, 58),   # 第3个卡钻：4小时35分 - 4小时50分
        (75, 82),   # 第4个卡钻：6小时15分 - 6小时50分
        (100, 105), # 第5个卡钻：8小时20分 - 8小时45分
    ]
    
    # 应用卡钻特征
    for start_idx, end_idx in stuck_periods:
        if end_idx < num_points:
            # 卡钻时的特征变化
            data['DEP'][start_idx:end_idx] = data['DEP'][start_idx]  # 井深不变
            data['BITDEP'][start_idx:end_idx] = data['BITDEP'][start_idx]  # 钻头位置不变
            data['RPM'][start_idx:end_idx] = np.random.normal(2, 1, end_idx-start_idx)  # 转速降低
            data['HOKHEI'][start_idx:end_idx] = data['HOKHEI'][start_idx]  # 大钩高度不变
            data['HKLD'][start_idx:end_idx] = np.random.normal(1200, 50, end_idx-start_idx)  # 负荷增加
            data['TOR'][start_idx:end_idx] = np.random.normal(35, 5, end_idx-start_idx)  # 扭矩增加
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    logger.info(f"长时间钻井数据已保存: {output_file}")
    logger.info(f"数据点数: {len(df)}, 时间范围: {df['date'].min()} 到 {df['date'].max()}")
    logger.info(f"模拟卡钻时间段: {len(stuck_periods)}个")
    
    return output_file


def demonstrate_segmentation():
    """演示数据分割功能"""
    logger.info("=== 数据分割功能演示 ===")
    
    # 创建长时间数据
    data_file = create_long_drilling_data("demo_long_data.csv", duration_hours=1)
    
    try:
        # 创建分割器
        segmentation = DataSegmentation(segment_duration_minutes=3)
        
        # 执行分割
        segment_dir, segment_files = segmentation.segment_data(data_file, "demo_segments")
        
        print(f"\n📊 分割结果:")
        print(f"原始文件: {data_file}")
        print(f"分割目录: {segment_dir}")
        print(f"片段数量: {len(segment_files)}")
        
        # 显示前几个片段文件名
        print(f"\n📋 片段文件示例:")
        for i, segment_file in enumerate(segment_files[:5]):
            filename = os.path.basename(segment_file)
            # 读取片段数据
            segment_df = pd.read_csv(segment_file)
            print(f"  {i+1}. {filename} - {len(segment_df)}行数据")
        
        if len(segment_files) > 5:
            print(f"  ... 还有 {len(segment_files) - 5} 个片段")
        
        # 清理
        segmentation.cleanup_temp_dirs()
        
    except Exception as e:
        logger.error(f"分割演示失败: {str(e)}")
    
    finally:
        # 清理演示文件
        if os.path.exists(data_file):
            os.remove(data_file)
        
        # 清理分割目录
        import shutil
        if os.path.exists("demo_segments"):
            shutil.rmtree("demo_segments")


def demonstrate_segment_detection():
    """演示片段级别检测"""
    logger.info("=== 片段级别检测演示 ===")
    
    # 创建长时间数据
    data_file = create_long_drilling_data("demo_detection_data.csv", duration_hours=1)
    
    try:
        # 创建检测系统
        detector = UnifiedDetectionSystem()
        
        # 执行检测
        result = detector.detect_single_file(data_file)
        
        print(f"\n📊 检测结果:")
        print(f"文件: {result['filename']}")
        print(f"整体风险分数: {result['risk']:.3f}")
        print(f"整体预测标签: {result['label']} ({'预警' if result['label'] == 1 else '正常'})")
        
        # 显示各算法的片段统计
        if 'details' in result:
            details = result['details']
            
            print(f"\n📋 各算法片段统计:")
            for algo_name in ['precursor', 'anomaly', 'expert']:
                if algo_name in details:
                    algo_result = details[algo_name]
                    segment_count = algo_result.get('segment_count', 0)
                    alert_segments = algo_result.get('alert_segments', 0)
                    confidence = algo_result.get('confidence', 0.0)
                    
                    print(f"  {algo_name}: {segment_count}个片段, {alert_segments}个预警, 置信度{confidence:.1%}")
        
        # 生成片段级别的结果
        segment_results = detector._extract_segment_results(result)
        
        print(f"\n📈 片段级别结果:")
        print(f"总片段数: {len(segment_results)}")
        
        # 显示预警片段
        alert_segments = [s for s in segment_results if s['Predicted_Label'] == 1]
        print(f"预警片段数: {len(alert_segments)}")
        
        if alert_segments:
            print(f"\n🚨 预警片段详情:")
            for i, segment in enumerate(alert_segments[:5]):
                print(f"  {i+1}. {segment['Filename']} - 风险: {segment['Risk']:.3f}")
            
            if len(alert_segments) > 5:
                print(f"  ... 还有 {len(alert_segments) - 5} 个预警片段")
        
    except Exception as e:
        logger.error(f"片段检测演示失败: {str(e)}")
    
    finally:
        # 清理演示文件
        if os.path.exists(data_file):
            os.remove(data_file)


def demonstrate_batch_segment_detection():
    """演示批量片段检测"""
    logger.info("=== 批量片段检测演示 ===")
    
    # 创建多个测试文件
    demo_dir = "demo_batch_segments"
    os.makedirs(demo_dir, exist_ok=True)
    
    test_files = []
    
    try:
        # 创建3个不同的测试文件
        for i in range(3):
            test_file = os.path.join(demo_dir, f"well_{i+1}_data.csv")
            create_long_drilling_data(test_file, duration_hours=1)
            test_files.append(test_file)
        
        # 创建检测系统
        detector = UnifiedDetectionSystem()
        
        # 执行批量检测（片段输出模式）
        output_file = detector.batch_detect(
            input_dir=demo_dir,
            pattern="*.csv",
            segment_output=True
        )
        
        # 读取并分析结果
        results_df = pd.read_csv(output_file)
        
        print(f"\n📊 批量片段检测结果:")
        print(f"输出文件: {output_file}")
        print(f"总片段数: {len(results_df)}")
        print(f"预警片段数: {len(results_df[results_df['Predicted_Label'] == 1])}")
        print(f"预警比例: {len(results_df[results_df['Predicted_Label'] == 1])/len(results_df)*100:.1f}%")
        
        # 按原始文件分组统计
        print(f"\n📋 按文件分组统计:")
        for test_file in test_files:
            filename_pattern = os.path.splitext(os.path.basename(test_file))[0]
            file_segments = results_df[results_df['Filename'].str.contains(filename_pattern)]
            file_alerts = len(file_segments[file_segments['Predicted_Label'] == 1])
            
            print(f"  {os.path.basename(test_file)}: {len(file_segments)}个片段, {file_alerts}个预警")
        
        # 显示部分结果
        print(f"\n📈 结果示例:")
        print(results_df.head(10).to_string(index=False))
        
    except Exception as e:
        logger.error(f"批量片段检测演示失败: {str(e)}")
    
    finally:
        # 清理演示文件
        for test_file in test_files:
            if os.path.exists(test_file):
                os.remove(test_file)
        
        if os.path.exists(demo_dir):
            import shutil
            shutil.rmtree(demo_dir)


def main():
    """主函数"""
    print("🚀 片段处理功能演示")
    print("=" * 50)
    
    try:
        # 数据分割演示
        demonstrate_segmentation()
        
        print("\n" + "=" * 50)
        
        # 片段检测演示
        demonstrate_segment_detection()
        
        print("\n" + "=" * 50)
        
        # 批量片段检测演示
        demonstrate_batch_segment_detection()
        
        print("\n" + "=" * 50)
        print("✅ 所有片段处理演示完成！")
        
    except Exception as e:
        logger.error(f"演示执行失败: {str(e)}")
        print(f"❌ 演示执行失败: {str(e)}")


if __name__ == "__main__":
    main()
