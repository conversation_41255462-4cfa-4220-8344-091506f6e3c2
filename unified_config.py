#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一检测系统配置文件

作者：AI Assistant
创建时间：2025-01-27
"""

import os
from typing import Dict, List


class UnifiedConfig:
    """统一检测系统配置类"""
    
    # 默认权重配置
    DEFAULT_WEIGHTS = {
        'precursor': 0.5,    # 前驱信号检测权重
        'anomaly': 0.3,      # 异常检测权重
        'expert': 0.2        # 专家经验权重
    }
    
    # 标准特征列（10维）
    STANDARD_FEATURES = [
        'DEP',      # 井深
        'BITDEP',   # 钻头位置
        'HOKHEI',   # 大钩高度
        'DRITIME',  # 迟到时间
        'WOB',      # 钻压
        'HKLD',     # 大钩负荷
        'RPM',      # 转盘转速
        'TOR',      # 扭矩
        'SPP',      # 立压
        'date'      # 时间戳
    ]
    
    # 列名映射字典
    COLUMN_MAPPING = {
        # 标准列名保持不变
        'DEP': 'DEP',
        'BITDEP': 'BITDEP', 
        'HOKHEI': 'HOKHEI',
        'DRITIME': 'DRITIME',
        'WOB': 'WOB',
        'HKLD': 'HKLD',
        'RPM': 'RPM',
        'TOR': 'TOR',
        'SPP': 'SPP',
        'date': 'date',
        
        # 中文列名映射
        '井深': 'DEP',
        '钻头位置': 'BITDEP',
        '大钩高度': 'HOKHEI',
        '迟到时间': 'DRITIME',
        '钻压': 'WOB',
        '大钩负荷': 'HKLD',
        '转盘转速': 'RPM',
        '扭矩': 'TOR',
        '立压': 'SPP',
        '时间': 'date',
        '日期': 'date',
        
        # 英文变体
        'Well_Depth': 'DEP',
        'Bit_Depth': 'BITDEP',
        'Hook_Height': 'HOKHEI',
        'Hookload': 'HKLD',
        'Torque': 'TOR',
        'Standpipe_Pressure': 'SPP'
    }
    
    # 前驱信号检测配置
    PRECURSOR_CONFIG = {
        'script_path': '前驱信号检测/run.py',
        'task_name': 'earlysignaldet',
        'model': 'PatchTST',
        'seq_len': 96,
        'enc_in': 10,
        'd_model': 128,
        'batch_size': 1
    }
    
    # 异常检测配置
    ANOMALY_CONFIG = {
        'script_path': '异常检测/run.py',
        'task_name': 'anomaly_detection',
        'model': 'FEDformer',
        'seq_len': 96,
        'enc_in': 12,
        'd_model': 128,
        'anomaly_threshold': 10.0  # 异常比例阈值(%)
    }
    
    # 专家经验配置
    EXPERT_CONFIG = {
        'window_size': 30,
        'score_threshold': 4,
        'confidence_threshold': 0.85,
        'default_flow_in': 30.0,
        'default_flow_out': 25.0,
        'rules': {
            'depth_change_threshold': 0.001,
            'bit_change_threshold': 0.001,
            'rpm_threshold': 5,
            'hook_height_change_threshold': 0.01,
            'hookload_change_threshold': 3,
            'flow_diff_threshold': 15
        }
    }
    
    # 融合配置
    FUSION_CONFIG = {
        'label_threshold': 0.5,  # 标签融合阈值
        'risk_normalization': True,  # 是否对风险分数进行标准化
        'min_algorithms': 1  # 至少需要成功运行的算法数量
    }
    
    # 输出配置
    OUTPUT_CONFIG = {
        'encoding': 'utf-8-sig',
        'timestamp_format': '%Y%m%d_%H%M%S',
        'result_columns': ['Filename', 'Risk', 'Predicted_Label'],
        'detailed_output': False  # 是否输出详细结果
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'file_enabled': False,
        'file_path': 'unified_detection.log'
    }
    
    @classmethod
    def get_algorithm_paths(cls) -> Dict[str, str]:
        """获取算法脚本路径"""
        return {
            'precursor': cls.PRECURSOR_CONFIG['script_path'],
            'anomaly': cls.ANOMALY_CONFIG['script_path'],
            'expert': None  # 专家系统内置实现
        }
    
    @classmethod
    def validate_weights(cls, weights: Dict[str, float]) -> bool:
        """验证权重配置"""
        if not isinstance(weights, dict):
            return False
        
        required_keys = {'precursor', 'anomaly', 'expert'}
        if set(weights.keys()) != required_keys:
            return False
        
        if abs(sum(weights.values()) - 1.0) > 1e-6:
            return False
        
        return all(0 <= w <= 1 for w in weights.values())
    
    @classmethod
    def get_default_config(cls) -> Dict:
        """获取默认配置"""
        return {
            'weights': cls.DEFAULT_WEIGHTS.copy(),
            'features': cls.STANDARD_FEATURES.copy(),
            'column_mapping': cls.COLUMN_MAPPING.copy(),
            'precursor': cls.PRECURSOR_CONFIG.copy(),
            'anomaly': cls.ANOMALY_CONFIG.copy(),
            'expert': cls.EXPERT_CONFIG.copy(),
            'fusion': cls.FUSION_CONFIG.copy(),
            'output': cls.OUTPUT_CONFIG.copy(),
            'logging': cls.LOGGING_CONFIG.copy()
        }
    
    @classmethod
    def load_config_from_file(cls, config_file: str) -> Dict:
        """从文件加载配置"""
        import json
        
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 合并默认配置
        default_config = cls.get_default_config()
        for key, value in config.items():
            if key in default_config:
                if isinstance(value, dict) and isinstance(default_config[key], dict):
                    default_config[key].update(value)
                else:
                    default_config[key] = value
        
        return default_config
    
    @classmethod
    def save_config_to_file(cls, config: Dict, config_file: str):
        """保存配置到文件"""
        import json
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def create_sample_config(cls, output_file: str = "unified_config.json"):
        """创建示例配置文件"""
        config = cls.get_default_config()
        
        # 添加注释说明
        config['_description'] = {
            'weights': '算法权重配置，三个权重之和必须为1.0',
            'features': '标准特征列表，共10维',
            'precursor': '前驱信号检测算法配置',
            'anomaly': '异常检测算法配置',
            'expert': '专家经验算法配置',
            'fusion': '结果融合配置',
            'output': '输出格式配置',
            'logging': '日志配置'
        }
        
        cls.save_config_to_file(config, output_file)
        print(f"示例配置文件已创建: {output_file}")


# 全局配置实例
CONFIG = UnifiedConfig()


if __name__ == "__main__":
    # 创建示例配置文件
    UnifiedConfig.create_sample_config()
