#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一检测系统使用示例

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import pandas as pd
import numpy as np
from unified_detection_system import UnifiedDetectionSystem
from unified_config import CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_data(output_file: str = "sample_data.csv", num_rows: int = 200):
    """
    创建示例数据文件
    
    Args:
        output_file: 输出文件路径
        num_rows: 数据行数
    """
    logger.info(f"创建示例数据文件: {output_file}")
    
    # 生成模拟钻井数据
    np.random.seed(42)
    
    data = {
        'date': pd.date_range('2025-01-01', periods=num_rows, freq='5min'),
        'DEP': np.cumsum(np.random.normal(0.1, 0.05, num_rows)) + 3000,  # 井深递增
        'BITDEP': np.cumsum(np.random.normal(0.1, 0.05, num_rows)) + 3000,  # 钻头位置
        'HOKHEI': np.random.normal(25, 2, num_rows),  # 大钩高度
        'DRITIME': np.random.normal(20, 5, num_rows),  # 迟到时间
        'WOB': np.random.normal(150, 30, num_rows),  # 钻压
        'HKLD': np.random.normal(900, 100, num_rows),  # 大钩负荷
        'RPM': np.random.normal(80, 15, num_rows),  # 转盘转速
        'TOR': np.random.normal(20, 5, num_rows),  # 扭矩
        'SPP': np.random.normal(25, 5, num_rows),  # 立压
    }
    
    # 在某些时间段模拟卡钻情况
    stuck_periods = [(50, 70), (120, 140), (180, 190)]
    
    for start, end in stuck_periods:
        # 卡钻时的特征变化
        data['DEP'][start:end] = data['DEP'][start]  # 井深不变
        data['BITDEP'][start:end] = data['BITDEP'][start]  # 钻头位置不变
        data['RPM'][start:end] = np.random.normal(2, 1, end-start)  # 转速降低
        data['HOKHEI'][start:end] = data['HOKHEI'][start]  # 大钩高度不变
        data['HKLD'][start:end] = np.random.normal(1200, 50, end-start)  # 负荷增加
        data['TOR'][start:end] = np.random.normal(35, 5, end-start)  # 扭矩增加
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    logger.info(f"示例数据已保存到: {output_file}")
    logger.info(f"数据形状: {df.shape}")
    logger.info(f"模拟卡钻时间段: {stuck_periods}")
    
    return output_file


def example_single_file_detection():
    """单文件检测示例"""
    logger.info("=== 单文件检测示例 ===")
    
    # 创建示例数据
    sample_file = create_sample_data("sample_drilling_data.csv")
    
    try:
        # 创建检测系统
        detector = UnifiedDetectionSystem()
        
        # 执行检测
        result = detector.detect_single_file(sample_file)
        
        # 显示结果
        print(f"\n📊 检测结果:")
        print(f"文件: {result['filename']}")
        print(f"风险分数: {result['risk']:.3f}")
        print(f"预测标签: {result['label']} ({'预警' if result['label'] == 1 else '正常'})")
        
        # 显示详细信息
        if 'details' in result:
            details = result['details']
            print(f"\n📋 详细结果:")
            for algo_name, algo_result in details.items():
                if algo_name != 'weighted_label_score' and isinstance(algo_result, dict):
                    risk = algo_result.get('risk', 0)
                    label = algo_result.get('label', 0)
                    print(f"  {algo_name}: 风险={risk:.3f}, 标签={label}")
        
    except Exception as e:
        logger.error(f"单文件检测失败: {str(e)}")
    
    finally:
        # 清理示例文件
        if os.path.exists(sample_file):
            os.remove(sample_file)


def example_batch_detection():
    """批量检测示例"""
    logger.info("=== 批量检测示例 ===")
    
    # 创建示例数据目录
    sample_dir = "sample_batch_data"
    os.makedirs(sample_dir, exist_ok=True)
    
    sample_files = []
    
    try:
        # 创建多个示例文件
        for i in range(3):
            sample_file = os.path.join(sample_dir, f"well_{i+1}_data.csv")
            create_sample_data(sample_file, num_rows=150)
            sample_files.append(sample_file)
        
        # 创建检测系统（自定义权重）
        custom_weights = {'precursor': 0.4, 'anomaly': 0.4, 'expert': 0.2}
        detector = UnifiedDetectionSystem(weights=custom_weights)
        
        # 执行批量检测
        output_file = detector.batch_detect(sample_dir, pattern="*.csv")
        
        # 读取并显示结果
        results_df = pd.read_csv(output_file)
        print(f"\n📊 批量检测结果:")
        print(results_df.to_string(index=False))
        
        # 统计信息
        total_files = len(results_df)
        alert_files = len(results_df[results_df['Predicted_Label'] == 1])
        print(f"\n📈 统计信息:")
        print(f"总文件数: {total_files}")
        print(f"预警文件数: {alert_files}")
        print(f"预警比例: {alert_files/total_files*100:.1f}%")
        
    except Exception as e:
        logger.error(f"批量检测失败: {str(e)}")
    
    finally:
        # 清理示例文件和目录
        for sample_file in sample_files:
            if os.path.exists(sample_file):
                os.remove(sample_file)
        
        if os.path.exists(sample_dir):
            os.rmdir(sample_dir)


def example_custom_config():
    """自定义配置示例"""
    logger.info("=== 自定义配置示例 ===")
    
    # 创建自定义配置
    custom_config = CONFIG.get_default_config()
    
    # 修改权重配置
    custom_config['weights'] = {'precursor': 0.6, 'anomaly': 0.2, 'expert': 0.2}
    
    # 修改专家系统阈值
    custom_config['expert']['score_threshold'] = 3
    
    # 修改异常检测阈值
    custom_config['anomaly']['anomaly_threshold'] = 15.0
    
    print(f"📋 自定义配置:")
    print(f"权重配置: {custom_config['weights']}")
    print(f"专家系统阈值: {custom_config['expert']['score_threshold']}")
    print(f"异常检测阈值: {custom_config['anomaly']['anomaly_threshold']}%")
    
    # 使用自定义配置创建检测系统
    detector = UnifiedDetectionSystem(config=custom_config)
    
    print(f"✅ 自定义配置检测系统创建成功")


def main():
    """主函数"""
    print("🚀 统一钻井检测系统使用示例")
    print("=" * 50)
    
    try:
        # 单文件检测示例
        example_single_file_detection()
        
        print("\n" + "=" * 50)
        
        # 批量检测示例
        example_batch_detection()
        
        print("\n" + "=" * 50)
        
        # 自定义配置示例
        example_custom_config()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"示例执行失败: {str(e)}")
        print(f"❌ 示例执行失败: {str(e)}")


if __name__ == "__main__":
    main()
